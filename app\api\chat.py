from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Optional
from app.schemas.chat_schema import ChatRequest, ChatResponse, TemplateListResponse
from app.services.openai_service import OpenAIService
from app.services.langchain_service import LangchainService
from dotenv import load_dotenv
import os
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.models import UserApiKey
import logging
from openai import OpenAI

load_dotenv()

router = APIRouter()
openai_api_key = os.getenv("OPENAI_API_KEY")

# Create service instances
openai_service = OpenAIService()
langchain_service = LangchainService(openai_api_key=openai_api_key)

# Setup logging
logger = logging.getLogger(__name__)

# Request models
class ApiKeyRequest(BaseModel):
    api_key: str = Field(..., description="Your OpenAI API key")
    user_id: Optional[str] = Field(None, description="User identifier (optional)")

# Template endpoints
@router.get("/templates", response_model=TemplateListResponse)
async def list_templates():
    """Get a list of available prompt templates"""
    templates = langchain_service.get_available_templates()
    return TemplateListResponse(templates=templates)

# API Key management endpoints
@router.post("/api-keys", response_model=dict)
async def create_api_key(request: ApiKeyRequest, db: Session = Depends(get_db)):
    """Set or update an OpenAI API key for a user or as default"""
    try:
        result = openai_service.set_api_key(request.api_key, request.user_id, db)
        return {"message": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api-keys/{user_id}", response_model=dict)
async def check_api_key(user_id: str, db: Session = Depends(get_db)):
    """Check if a user has an API key stored"""
    user_key = db.query(UserApiKey).filter(UserApiKey.user_id == user_id).first()
    return {
        "user_id": user_id,
        "has_api_key": user_key is not None
    }

@router.delete("/api-keys/{user_id}", response_model=dict)
async def delete_api_key(user_id: str, db: Session = Depends(get_db)):
    """Remove a user's API key"""
    result = openai_service.remove_api_key(user_id, db)
    return {"message": result}

@router.post("/api-keys/default", response_model=dict)
async def set_default_api_key(request: ApiKeyRequest, db: Session = Depends(get_db)):
    """Set the default API key (used when no user key is available)"""
    try:
        result = openai_service.set_api_key(request.api_key, None, db)
        return {"message": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api-keys/validate", response_model=dict)
async def validate_api_key(request: ApiKeyRequest):
    """Validate an OpenAI API key without storing it"""
    try:
        # Test the provided API key
        client = OpenAI(api_key=request.api_key)
        test_response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "test"}],
            max_tokens=5
        )
        return {"valid": True, "message": "API key is valid"}
    except Exception as e:
        return {"valid": False, "message": f"Invalid API key: {str(e)}"}

# Chat endpoint
@router.post("/chat", response_model=ChatResponse)
async def create_chat(chat_request: ChatRequest, user_id: Optional[str] = None, db: Session = Depends(get_db)):
    """Generate a chat response using the specified template and user's API key"""
    try:
        # Set template if specified in the request
        template_used = "general"  # default
        
        # Parse the template if it includes parameters
        if chat_request.template:
            # Handle template setting
            pass
        
        # Process the message with Langchain
        processed_message = langchain_service.process_message(chat_request.message)
        
        try:
            # Generate a response using the user's API key if provided
            response = openai_service.generate_response(processed_message, user_id, db)
        except ValueError as e:
            if "No API key found for user" in str(e):
                return ChatResponse(
                    response=f"Error: {str(e)}. Please set an API key using the /api-keys endpoint.",
                    template_used=template_used
                )
            raise
        
        return ChatResponse(
            response=response,
            template_used=template_used
        )
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))