from fastapi import FastAPI
from app.api.chat import router as chat_router
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(
    title="FastAPI Chat Application",
    version="1.0.0",
    description="A simple chat API powered by FastAPI and GPT-4."
)

app.include_router(chat_router)

@app.get("/")
def read_root():
    return {
        "message": "Welcome to the FastAPI Chat Application",
        "model": "gpt-4"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)