from pydantic import BaseModel, Field
from typing import Optional, Dict, List

class ChatRequest(BaseModel):
    message: str
    template: Optional[str] = Field(None, description="The prompt template to use for this message")

class ChatResponse(BaseModel):
    response: str
    template_used: Optional[str] = None

class TemplateListResponse(BaseModel):
    templates: List[str]

class TemplateDetailResponse(BaseModel):
    templates: Dict[str, List[str]]