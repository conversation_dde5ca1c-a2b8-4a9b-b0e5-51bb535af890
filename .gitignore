# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
pytest_cache/
.coverage
htmlcov/
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Virtual environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Environment variables
#.env

# Logs
logs/
*.log

# Local development
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Project specific
*.db