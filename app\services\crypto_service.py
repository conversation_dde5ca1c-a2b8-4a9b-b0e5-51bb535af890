import logging
import base64
from cryptography.fernet import Fernet
import os
import re

# Set up logging
logger = logging.getLogger(__name__)

class CryptoService:
    def __init__(self):
        logger.info("Initializing CryptoService")
        # Get encryption key
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            logger.error("No ENCRYPTION_KEY found in environment variables")
            raise ValueError("ENCRYPTION_KEY environment variable is required")
        
        try:
            if isinstance(key, str):
                key = key.encode()
            
            self.fernet = Fernet(key)
            logger.info("Fernet initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing Fernet: {str(e)}")
            raise ValueError(f"Invalid encryption key format: {str(e)}")
    
    def encrypt(self, text):
        """Encrypt text to bytes, then convert to base64 string for storage"""
        logger.info("Encrypting data")
        try:
            if not isinstance(text, str):
                text = str(text)
            
            # Encrypt to bytes
            encrypted_bytes = self.fernet.encrypt(text.encode())
            
            # Convert to base64 string for storage
            encrypted_str = base64.b64encode(encrypted_bytes).decode('utf-8')
            logger.info(f"Data encrypted successfully, length: {len(encrypted_str)}")
            return encrypted_str
        except Exception as e:
            logger.error(f"Encryption error: {str(e)}")
            raise
    
    def decrypt(self, encrypted_data):
        """Decrypt from string or bytes"""
        logger.info("Decrypting data")
        try:
            # Check if this is an escaped bytes string like '\x67...'
            if isinstance(encrypted_data, str) and encrypted_data.startswith('\\x'):
                logger.info("Detected escaped byte string format")
                try:
                    # Try to convert from escaped string to bytes
                    # Extract the hex values
                    hex_values = re.findall(r'\\x([0-9a-fA-F]{2})', encrypted_data)
                    if hex_values:
                        # Convert hex to bytes
                        byte_values = bytes([int(h, 16) for h in hex_values])
                        encrypted_data = byte_values
                    else:
                        logger.warning("Could not parse escaped byte string")
                except Exception as e:
                    logger.error(f"Error converting escaped string: {str(e)}")
            
            # If it's a base64 string, decode it first
            if isinstance(encrypted_data, str):
                try:
                    encrypted_data = base64.b64decode(encrypted_data)
                except Exception as e:
                    logger.warning(f"Not valid base64, trying direct encoding: {str(e)}")
                    encrypted_data = encrypted_data.encode()
            
            # Now decrypt the bytes
            decrypted = self.fernet.decrypt(encrypted_data).decode()
            logger.info("Data decrypted successfully")
            return decrypted
        except Exception as e:
            logger.error(f"Decryption error: {str(e)}")
            raise