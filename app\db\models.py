from sqlalchemy import Column, String, DateTime, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class UserApiKey(Base):
    __tablename__ = "user_api_keys"
    
    user_id = Column(String, primary_key=True)
    # Store encrypted API key, not plaintext
    encrypted_api_key = Column(String, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())