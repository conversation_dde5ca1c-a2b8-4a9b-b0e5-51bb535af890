from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
from app.db.models import UserApiKey
from app.services.crypto_service import CryptoService
import re

# Load environment variables
load_dotenv()

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

# Create crypto service
crypto_service = CryptoService()

def fix_keys():
    print("Starting database key repair process...")
    
    # Get all user keys
    keys = db.query(UserApiKey).all()
    print(f"Found {len(keys)} keys in database")
    
    for key in keys:
        user_id = key.user_id
        encrypted_key = key.encrypted_api_key
        
        print(f"Processing key for user {user_id}")
        print(f"  Current format: {type(encrypted_key)}")
        print(f"  Length: {len(str(encrypted_key))}")
        
        # Check if it looks like escaped bytes
        if isinstance(encrypted_key, str) and '\\x' in encrypted_key:
            print("  Detected escaped byte string, attempting to fix...")
            
            try:
                # Get the actual API key - this will try multiple conversion methods
                api_key = input(f"Enter the actual API key for user {user_id}: ")
                
                # Re-encrypt with the corrected method
                new_encrypted = crypto_service.encrypt(api_key)
                
                # Update the database
                key.encrypted_api_key = new_encrypted
                db.commit()
                
                print(f"  Key for user {user_id} successfully updated")
                
                # Verify it works
                try:
                    decrypted = crypto_service.decrypt(new_encrypted)
                    if decrypted == api_key:
                        print("  Verification successful!")
                    else:
                        print("  WARNING: Decrypted key doesn't match original!")
                except Exception as e:
                    print(f"  ERROR: Verification failed: {str(e)}")
                
            except Exception as e:
                print(f"  ERROR fixing key: {str(e)}")
                continue
            
    print("Key repair process complete")

if __name__ == "__main__":
    fix_keys()