# FastAPI Chat Application

A flexible chat API powered by FastAPI, OpenAI, and LangChain that supports multiple prompt templates and user-specific API keys.

## Features

- Multiple AI persona templates (technical expert, creative writer, etc.)
- Custom prompts with configurable parameters
- Support for question generation, interviews, and quizzes
- Secure API key management per user
- PostgreSQL database for persistent storage
- Encrypted storage of API keys
- Markdown-formatted responses

## Setup Instructions

1. Clone the repository:
   ```
   git clone <repository-url>
   cd fastapi-chat-app
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows:
     ```
     venv\Scripts\activate
     ```
   - On macOS/Linux:
     ```
     source venv/bin/activate
     ```

4. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

5. Set up PostgreSQL:
   - Create a new PostgreSQL database
   - Update the `DATABASE_URL` in your `.env` file
   - For Azure PostgreSQL, ensure your IP is added to the firewall rules

6. Generate an encryption key:
   ```python
   python -c "from cryptography.fernet import Fernet; print(f'ENCRYPTION_KEY={Fernet.generate_key().decode()}')"
   ```

7. Create a `.env` file in the project root:
   ```
   # OpenAI API key (optional, users can provide their own)
   OPENAI_API_KEY=your_openai_api_key_here
   
   # Database connection
   DATABASE_URL=********************************************/dbname?sslmode=require
   
   # Security
   ENCRYPTION_KEY=your_generated_encryption_key
   ```

8. Initialize and run database migrations:
   ```
   alembic init migrations
   # Update migrations/env.py to import Base from app.db.models
   alembic revision --autogenerate -m "Create initial tables"
   alembic upgrade head
   ```

## Usage

To run the FastAPI application:

```
uvicorn app.main:app --reload
```

Access the API documentation at `http://127.0.0.1:8000/docs`.

## API Endpoints

### User API Key Management

- **POST /api-keys**
  - Set or update an OpenAI API key for a specific user
  - Body: `{"api_key": "sk-...", "user_id": "user123"}`

- **GET /api-keys/{user_id}**
  - Check if a user has an API key stored
  - Returns: `{"user_id": "user123", "has_api_key": true}`

- **DELETE /api-keys/{user_id}**
  - Remove an API key for a specific user

- **POST /api-keys/default**
  - Set a default API key that's used when no user key is available
  - Body: `{"api_key": "sk-..."}`

- **POST /api-keys/validate**
  - Validate an OpenAI API key without storing it
  - Body: `{"api_key": "sk-..."}`
  - Returns: `{"valid": true, "message": "API key is valid"}`

### Templates

- **GET /templates**
  - Get a list of available prompt templates

### Chat

- **POST /chat**
  - Send a message and get an AI-generated response
  - Optionally specify a user ID to use their stored API key
  - Optionally specify a prompt template

#### Request Format:

```json
{
  "message": "Your message here",
  "template": "technical"
}
```

With user ID as a query parameter:
```
POST /chat?user_id=user123
```

## Available Templates

The API supports various specialized templates:

### Persona Templates

- **general**: Default helpful assistant
- **technical**: Technical expert for programming and computer science
- **creative**: Creative writer with a vivid imagination
- **business**: Business consultant for professional advice
- **academic**: Academic researcher for scholarly responses
- **coach**: Supportive life coach for encouragement and advice

### Question Generation Templates

These templates accept a `num_questions` parameter:

- **questions**: Generates thoughtful questions on any topic
- **interview**: Creates professional interview questions
- **socratic**: Uses the Socratic method for deeper understanding
- **quiz**: Creates a multiple-choice quiz with questions and answers

### Using Templates with Parameters

```json
{
  "message": "Python programming",
  "template": "quiz num_questions=7"
}
```

## Example Workflow

1. **Validate your API key**:
   ```bash
   curl -X POST http://localhost:8000/api-keys/validate \
     -H "Content-Type: application/json" \
     -d '{"api_key": "sk-your-api-key"}'
   ```

2. **Store your API key**:
   ```bash
   curl -X POST http://localhost:8000/api-keys \
     -H "Content-Type: application/json" \
     -d '{"api_key": "sk-your-api-key", "user_id": "your-username"}'
   ```

3. **Send a chat message**:
   ```bash
   curl -X POST "http://localhost:8000/chat?user_id=your-username" \
     -H "Content-Type: application/json" \
     -d '{"message": "What is machine learning?", "template": "technical"}'
   ```

## Troubleshooting

### Database Connection Issues
- Ensure your IP address is allowed in the database firewall rules
- Verify your username and password are correct
- For Azure PostgreSQL, use `?sslmode=require` in your connection string

### API Key Issues
- Verify your OpenAI API key is valid and has appropriate permissions
- Ensure the ENCRYPTION_KEY is properly formatted (32-byte base64-encoded)
- If keys don't work, try setting a new one with the `/api-keys` endpoint

## License

This project is licensed under the MIT License.