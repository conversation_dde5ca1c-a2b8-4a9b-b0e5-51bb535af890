from langchain_openai import Chat<PERSON>penAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder, HumanMessagePromptTemplate, SystemMessagePromptTemplate
from langchain_core.messages import SystemMessage
from typing import Dict, Optional

class LangchainService:
    def __init__(self, openai_api_key: str):
        self.llm = ChatOpenAI(api_key=openai_api_key, model="gpt-4")
        
        # Create a library of prompt templates with customizable parameters and system messages
        self.prompt_templates: Dict[str, object] = {
            "general": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are a helpful assistant designed to provide clear and informative responses."),
                    HumanMessagePromptTemplate.from_template("{message}")
                ]),
                "params": {}
            },
            "technical": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are a technical expert specializing in programming and computer science. Provide detailed technical explanations and code examples when relevant."),
                    HumanMessagePromptTemplate.from_template("{message}")
                ]),
                "params": {}
            },
            "creative": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are a creative writer with a vivid imagination. Craft engaging and imaginative responses."),
                    HumanMessagePromptTemplate.from_template("{message}")
                ]),
                "params": {}
            },
            "business": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are a business consultant with expertise in strategy, marketing, and operations. Provide professional business advice."),
                    HumanMessagePromptTemplate.from_template("{message}")
                ]),
                "params": {}
            },
            "academic": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are an academic researcher with extensive knowledge across multiple disciplines. Provide scholarly, well-referenced responses."),
                    HumanMessagePromptTemplate.from_template("{message}")
                ]),
                "params": {}
            },
            "coach": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are a supportive life coach focused on helping people achieve their goals. Be encouraging and provide actionable advice."),
                    HumanMessagePromptTemplate.from_template("{message}")
                ]),
                "params": {}
            },
            # Question templates with customizable parameters
            "questions": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are a question generator who creates thoughtful questions to explore topics deeply."),
                    HumanMessagePromptTemplate.from_template("Based on the topic provided, generate {num_questions} thoughtful questions that would help explore this subject more deeply. Aim for a mix of factual, conceptual, analytical, and thought-provoking questions. Topic: {message}")
                ]),
                "params": {"num_questions": 5}
            },
            "interview": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are an experienced interviewer who creates professional and revealing interview questions."),
                    HumanMessagePromptTemplate.from_template("Generate {num_questions} professional interview questions related to the following job or topic. Include a mix of technical, behavioral, and situational questions. Topic: {message}")
                ]),
                "params": {"num_questions": 5}
            },
            "socratic": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are a Socratic teacher who uses questions to guide people to deeper understanding."),
                    HumanMessagePromptTemplate.from_template("Channel the Socratic method to create a series of {num_questions} probing questions that would help someone develop deeper understanding of this topic through self-reflection. Start with basic questions and progressively move to more complex ones. Topic: {message}")
                ]),
                "params": {"num_questions": 5}
            },
            "quiz": {
                "template": ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template("You are an educational quiz creator who produces clear, informative multiple-choice questions."),
                    HumanMessagePromptTemplate.from_template("Create a short quiz with {num_questions} multiple-choice questions about the following topic. For each question, provide 4 options and indicate the correct answer. Topic: {message}")
                ]),
                "params": {"num_questions": 5}
            }
        }
        
        # Default prompt template
        self.current_template = "general"
        self.current_params = {}
        self.chain = self.prompt_templates[self.current_template]["template"] | self.llm

    def set_template(self, template_name: str, **kwargs) -> str:
        """
        Set the current prompt template to use with optional parameters
        Returns a message about the template change
        """
        if template_name in self.prompt_templates:
            self.current_template = template_name
            
            # Update params with defaults first, then any provided values
            self.current_params = self.prompt_templates[template_name]["params"].copy()
            
            # Override with any provided parameters
            for key, value in kwargs.items():
                if key in self.current_params:
                    try:
                        # Convert to appropriate type (e.g., string to int)
                        if isinstance(self.current_params[key], int):
                            self.current_params[key] = int(value)
                        else:
                            self.current_params[key] = value
                    except ValueError:
                        return f"Invalid value for parameter '{key}': {value}"
            
            self.chain = self.prompt_templates[template_name]["template"] | self.llm
            
            # If parameters were provided, include them in message
            if kwargs:
                param_str = ", ".join([f"{k}={v}" for k, v in self.current_params.items()])
                return f"Template changed to: {template_name} with parameters: {param_str}"
            return f"Template changed to: {template_name}"
        else:
            available_templates = ", ".join(self.prompt_templates.keys())
            return f"Template '{template_name}' not found. Available templates: {available_templates}"

    def get_available_templates(self) -> list:
        """Return a list of all available template names"""
        return list(self.prompt_templates.keys())

    def process_message(self, message: str, **kwargs) -> str:
        """Process the message using the current template"""
        # Check if this is a template change request with parameters
        if message.startswith("/template "):
            parts = message.replace("/template ", "").strip().split()
            if not parts:
                return "Please provide a template name"
                
            template_name = parts[0]
            
            # Parse parameters if provided
            params = {}
            for part in parts[1:]:
                if "=" in part:
                    key, value = part.split("=", 1)
                    params[key] = value
            
            return self.set_template(template_name, **params)
        
        # Combine default parameters for the current template with any specific ones for this message
        params = self.current_params.copy()
        params.update(kwargs)
        
        # Add the message to the parameters
        params["message"] = message
        
        response = self.chain.invoke(params)
        return response.content