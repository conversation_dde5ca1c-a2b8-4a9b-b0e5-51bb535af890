import logging
from openai import OpenAI
from typing import Optional
from app.db.models import User<PERSON><PERSON><PERSON>ey
from app.services.crypto_service import CryptoService
from sqlalchemy.orm import Session

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenAIService:
    def __init__(self):
        """Initialize the OpenAI service."""
        logger.info("Initializing OpenAIService")
        self.crypto_service = CryptoService()
        # Default client for anonymous requests
        self.default_client = None
    
    def set_api_key(self, api_key: str, user_id: Optional[str], db: Session) -> str:
        """Set or update the API key for a specific user or as default."""
        logger.info(f"Setting API key for user_id: {user_id if user_id else 'default'}")
        
        try:
            # Test the key with a simple API call
            logger.info("Testing API key validity")
            client = OpenAI(api_key=api_key)
            client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": "test"}],
                max_tokens=5
            )
            
            if user_id:
                # Encrypt the API key
                logger.info(f"Encrypting API key for user {user_id}")
                try:
                    encrypted_key = self.crypto_service.encrypt(api_key)
                    logger.info("API key encrypted successfully")
                except Exception as encrypt_error:
                    logger.error(f"Error encrypting API key: {str(encrypt_error)}")
                    raise ValueError(f"Error encrypting API key: {str(encrypt_error)}")
                
                # Store or update in database
                existing_key = db.query(UserApiKey).filter(UserApiKey.user_id == user_id).first()
                
                if existing_key:
                    logger.info(f"Updating existing API key for user {user_id}")
                    existing_key.encrypted_api_key = encrypted_key
                else:
                    logger.info(f"Creating new API key entry for user {user_id}")
                    new_key = UserApiKey(user_id=user_id, encrypted_api_key=encrypted_key)
                    db.add(new_key)
                
                try:
                    db.commit()
                    logger.info(f"Database updated for user {user_id}")
                except Exception as db_error:
                    logger.error(f"Database error: {str(db_error)}")
                    db.rollback()
                    raise ValueError(f"Database error: {str(db_error)}")
                    
                return f"API key updated successfully for user {user_id}"
            else:
                # Set as default client
                logger.info("Setting default client")
                self.default_client = client
                return "Default API key updated successfully"
                
        except Exception as e:
            logger.error(f"Error setting API key: {str(e)}")
            raise ValueError(f"Invalid API key: {str(e)}")
    
    def get_client(self, user_id: Optional[str], db: Session) -> OpenAI:
        """Get the OpenAI client for a specific user or the default client."""
        logger.info(f"Getting client for user_id: {user_id if user_id else 'default'}")
        
        if user_id:
            try:
                # Try to get user-specific key
                logger.info(f"Querying database for user {user_id}")
                user_key = db.query(UserApiKey).filter(UserApiKey.user_id == user_id).first()
                
                if user_key:
                    logger.info(f"Found API key record for user {user_id}")
                    try:
                        # Add specific error handling for decryption
                        logger.info("Attempting to decrypt API key")
                        # Debug: Log the encrypted key format/length for diagnosis
                        logger.info(f"Encrypted key type: {type(user_key.encrypted_api_key)}, Length: {len(str(user_key.encrypted_api_key)) if user_key.encrypted_api_key else 'None'}")
                        
                        decrypted_key = self.crypto_service.decrypt(user_key.encrypted_api_key)
                        logger.info("API key decrypted successfully")
                        return OpenAI(api_key=decrypted_key)
                    except Exception as decrypt_error:
                        logger.error(f"Error decrypting API key: {str(decrypt_error)}")
                        # If we can't decrypt, try the default client
                else:
                    logger.warning(f"No API key found for user {user_id}")
            except Exception as e:
                logger.error(f"Database error: {str(e)}")
        
        # Fall back to default client
        if self.default_client:
            logger.info(f"Using default client for {user_id if user_id else 'anonymous user'}")
            return self.default_client
            
        # No keys available
        logger.error(f"No API key available for user {user_id if user_id else 'anonymous'}")
        raise ValueError(f"No API key found for user '{user_id}' and no default API key is set.")
    
    def generate_response(self, message: str, user_id: Optional[str], db: Session) -> str:
        """Generate a response using the appropriate API key"""
        logger.info(f"Generating response for user {user_id if user_id else 'anonymous'}")
        
        try:
            client = self.get_client(user_id, db)
            logger.info("Client obtained successfully")
            
            response = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant. Format your responses using Markdown for better readability. Be direct and concise."},
                    {"role": "user", "content": message}
                ]
            )
            
            # Get the raw response
            raw_response = response.choices[0].message.content.strip()
            
            # Remove common prefacing phrases
            clean_response = self._remove_prefacing_phrases(raw_response)
            
            logger.info("Response generated and cleaned successfully")
            return clean_response
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            raise

    def _remove_prefacing_phrases(self, text):
        """Remove common prefacing phrases from responses"""
        prefacing_phrases = [
            "Here is the information in Markdown format.",
            "Here is the information",
            "Here's the information",
            "Here is the response",
            "Here's the response",
            "Sure, here is",
            "Sure, here's",
            "Sure,"
        ]
        
        # Check if the text starts with any of these phrases
        for phrase in prefacing_phrases:
            if text.startswith(phrase):
                # Remove the phrase and any whitespace/newlines that follow
                text = text[len(phrase):].lstrip(" \n")
                break
                
        return text
    
    def remove_api_key(self, user_id: str, db: Session) -> str:
        """Remove a user's API key"""
        logger.info(f"Removing API key for user {user_id}")
        
        try:
            user_key = db.query(UserApiKey).filter(UserApiKey.user_id == user_id).first()
            if user_key:
                logger.info(f"API key found for user {user_id}, deleting")
                db.delete(user_key)
                db.commit()
                logger.info("API key deleted successfully")
                return f"API key removed for user {user_id}"
            else:
                logger.warning(f"No API key found for user {user_id}")
                return f"No API key found for user {user_id}"
        except Exception as e:
            logger.error(f"Error removing API key: {str(e)}")
            raise